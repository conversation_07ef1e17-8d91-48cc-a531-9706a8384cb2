-- Migration script to update existing mock_tests table to unified data model
-- This script safely migrates existing data while preserving all information

-- Step 1: Create backup table
CREATE TABLE IF NOT EXISTS mock_tests_backup AS 
SELECT * FROM mock_tests;

-- Step 2: Add new columns to existing table (if they don't exist)
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS time TIME;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS test_type TEXT DEFAULT 'mock' CHECK (test_type IN ('practice', 'mock', 'actual_exam'));
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS test_environment TEXT CHECK (test_environment IN ('home', 'center', 'online'));
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS duration INTEGER;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS total_questions INTEGER;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS category_id TEXT;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS test_paper_url TEXT;

-- Goals and Preparation
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS target_score INTEGER;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS confidence_level INTEGER CHECK (confidence_level BETWEEN 1 AND 5);
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS preparation_time DECIMAL;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS study_materials JSONB DEFAULT '[]'::jsonb;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS expected_difficulty TEXT CHECK (expected_difficulty IN ('easy', 'medium', 'hard', 'very_hard'));

-- Performance Data
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS subject_performance JSONB DEFAULT '[]'::jsonb;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS time_spent INTEGER;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS time_spent_per_subject JSONB DEFAULT '{}'::jsonb;

-- Post-test Assessment
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard', 'very_hard'));
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS actual_difficulty TEXT CHECK (actual_difficulty IN ('easier', 'as_expected', 'harder'));
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS time_management TEXT CHECK (time_management IN ('excellent', 'good', 'average', 'poor'));
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS stress_level INTEGER CHECK (stress_level BETWEEN 1 AND 5);

-- Unified Analysis Data
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS syllabus JSONB DEFAULT '{"topics": [], "chapters": [], "overallProgress": 0}'::jsonb;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS mistakes JSONB DEFAULT '[]'::jsonb;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS takeaways JSONB DEFAULT '[]'::jsonb;

-- Analysis and Review Status
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS is_reviewed BOOLEAN DEFAULT FALSE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS analysis_completed BOOLEAN DEFAULT FALSE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS mistakes_analyzed BOOLEAN DEFAULT FALSE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS takeaways_recorded BOOLEAN DEFAULT FALSE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS review_scheduled TIMESTAMP WITH TIME ZONE;

-- Status and Workflow
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'completed' CHECK (status IN ('upcoming', 'in_progress', 'completed_pending', 'completed', 'missed'));
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS result_entered_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS is_from_upcoming BOOLEAN DEFAULT FALSE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS upcoming_test_id TEXT;

-- Workflow Settings
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS is_notification_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS auto_transition BOOLEAN DEFAULT FALSE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS preparation_reminders BOOLEAN DEFAULT FALSE;
ALTER TABLE mock_tests ADD COLUMN IF NOT EXISTS reminder_intervals JSONB DEFAULT '[]'::jsonb;

-- Step 3: Update existing data with default values
UPDATE mock_tests SET
    test_type = 'mock',
    target_score = 0,
    difficulty = 'medium',
    status = 'completed',
    completed_at = created_at,
    result_entered_at = created_at,
    is_reviewed = FALSE,
    analysis_completed = FALSE,
    mistakes_analyzed = FALSE,
    takeaways_recorded = FALSE,
    is_from_upcoming = FALSE,
    is_notification_enabled = FALSE,
    auto_transition = FALSE,
    preparation_reminders = FALSE
WHERE test_type IS NULL;

-- Step 4: Ensure NOT NULL constraints for required fields
ALTER TABLE mock_tests ALTER COLUMN total_marks_obtained SET DEFAULT 0;
ALTER TABLE mock_tests ALTER COLUMN total_marks SET DEFAULT 0;
ALTER TABLE mock_tests ALTER COLUMN subject_marks SET DEFAULT '[]'::jsonb;

-- Update any NULL values in required fields
UPDATE mock_tests SET 
    total_marks_obtained = 0 WHERE total_marks_obtained IS NULL;
UPDATE mock_tests SET 
    total_marks = 0 WHERE total_marks IS NULL;
UPDATE mock_tests SET 
    subject_marks = '[]'::jsonb WHERE subject_marks IS NULL;

-- Step 5: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_mock_tests_user_id ON mock_tests(user_id);
CREATE INDEX IF NOT EXISTS idx_mock_tests_test_date ON mock_tests(test_date);
CREATE INDEX IF NOT EXISTS idx_mock_tests_status ON mock_tests(status);
CREATE INDEX IF NOT EXISTS idx_mock_tests_test_type ON mock_tests(test_type);
CREATE INDEX IF NOT EXISTS idx_mock_tests_category_id ON mock_tests(category_id);
CREATE INDEX IF NOT EXISTS idx_mock_tests_created_at ON mock_tests(created_at);

-- Step 6: Create function to migrate local storage data to unified structure
CREATE OR REPLACE FUNCTION migrate_test_data_to_unified()
RETURNS void AS $$
BEGIN
    -- This function can be called to migrate any remaining local storage data
    -- Implementation would depend on specific migration needs
    RAISE NOTICE 'Mock test data migration to unified structure completed';
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_mock_tests_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_mock_tests_updated_at
    BEFORE UPDATE ON mock_tests
    FOR EACH ROW
    EXECUTE FUNCTION update_mock_tests_updated_at();

-- Step 8: Validation function to ensure data integrity
CREATE OR REPLACE FUNCTION validate_mock_test_data()
RETURNS void AS $$
DECLARE
    invalid_count INTEGER;
BEGIN
    -- Check for invalid test types
    SELECT COUNT(*) INTO invalid_count
    FROM mock_tests 
    WHERE test_type NOT IN ('practice', 'mock', 'actual_exam');
    
    IF invalid_count > 0 THEN
        RAISE WARNING 'Found % tests with invalid test_type', invalid_count;
    END IF;
    
    -- Check for invalid status values
    SELECT COUNT(*) INTO invalid_count
    FROM mock_tests 
    WHERE status NOT IN ('upcoming', 'in_progress', 'completed_pending', 'completed', 'missed');
    
    IF invalid_count > 0 THEN
        RAISE WARNING 'Found % tests with invalid status', invalid_count;
    END IF;
    
    -- Check for missing required fields
    SELECT COUNT(*) INTO invalid_count
    FROM mock_tests 
    WHERE name IS NULL OR name = '' OR user_id IS NULL OR test_date IS NULL;
    
    IF invalid_count > 0 THEN
        RAISE WARNING 'Found % tests with missing required fields', invalid_count;
    END IF;
    
    RAISE NOTICE 'Mock test data validation completed';
END;
$$ LANGUAGE plpgsql;

-- Run validation
SELECT validate_mock_test_data();

-- Step 9: Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON mock_tests TO authenticated;
-- GRANT USAGE ON SEQUENCE mock_tests_id_seq TO authenticated;

COMMIT;
