import {
  MockTest,
  UpcomingTest,
  UnifiedTestData,
  TestDataValidation,
  StandardTestFormData
} from '@/types/mockTest';
import { testDataValidator } from './testDataValidation';
import { testDataSyncService } from './testDataSyncService';
import { unifiedMockTestStorage, unifiedUpcomingTestStorage } from './unifiedTestStorage';
import { testTransitionService } from './testTransitionService';

export interface SyncValidationResult {
  success: boolean;
  errors: string[];
  warnings: string[];
  dataIntegrityIssues: string[];
  performanceMetrics: {
    validationTime: number;
    syncTime: number;
    totalTime: number;
  };
}

export interface TestSyncScenario {
  name: string;
  description: string;
  testFunction: () => Promise<SyncValidationResult>;
}

/**
 * Comprehensive testing and validation system for mock test synchronization
 */
export class TestSyncValidator {
  private static instance: TestSyncValidator;

  static getInstance(): TestSyncValidator {
    if (!TestSyncValidator.instance) {
      TestSyncValidator.instance = new TestSyncValidator();
    }
    return TestSyncValidator.instance;
  }

  // ============================================================================
  // COMPREHENSIVE VALIDATION SUITE
  // ============================================================================

  async runFullValidationSuite(userId: string): Promise<SyncValidationResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    const dataIntegrityIssues: string[] = [];

    try {
      console.log('🧪 Starting comprehensive mock test synchronization validation...');

      // Test 1: Data Model Validation
      await this.validateDataModels(userId, errors, warnings);

      // Test 2: Form Data Conversion
      await this.validateFormDataConversion(errors, warnings);

      // Test 3: Test Transition Logic
      await this.validateTestTransitions(userId, errors, warnings);

      // Test 4: Storage Synchronization
      await this.validateStorageSynchronization(userId, errors, warnings);

      // Test 5: Data Integrity Checks
      await this.validateDataIntegrity(userId, dataIntegrityIssues);

      // Test 6: Error Handling
      await this.validateErrorHandling(errors, warnings);

      const totalTime = Date.now() - startTime;

      console.log(`✅ Validation suite completed in ${totalTime}ms`);
      console.log(`📊 Results: ${errors.length} errors, ${warnings.length} warnings, ${dataIntegrityIssues.length} integrity issues`);

      return {
        success: errors.length === 0 && dataIntegrityIssues.length === 0,
        errors,
        warnings,
        dataIntegrityIssues,
        performanceMetrics: {
          validationTime: totalTime * 0.7, // Estimated
          syncTime: totalTime * 0.3, // Estimated
          totalTime
        }
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Validation suite failed: ${errorMessage}`);
      
      return {
        success: false,
        errors,
        warnings,
        dataIntegrityIssues,
        performanceMetrics: {
          validationTime: 0,
          syncTime: 0,
          totalTime: Date.now() - startTime
        }
      };
    }
  }

  // ============================================================================
  // INDIVIDUAL VALIDATION TESTS
  // ============================================================================

  private async validateDataModels(userId: string, errors: string[], warnings: string[]): Promise<void> {
    console.log('🔍 Testing data model validation...');

    // Test valid mock test
    const validMockTest: Partial<MockTest> = {
      id: 'test-mock-1',
      name: 'Sample Physics Test',
      date: '2024-01-15',
      userId,
      testType: 'mock',
      targetScore: 85,
      subjectMarks: [{
        subject: 'Physics',
        subjectColor: '#3b82f6',
        marksObtained: 85,
        totalMarks: 100
      }],
      totalMarksObtained: 85,
      totalMarks: 100,
      syllabus: { topics: ['Mechanics', 'Thermodynamics'], chapters: [], overallProgress: 0 },
      mistakes: [],
      takeaways: [],
      difficulty: 'medium',
      isReviewed: false,
      analysisCompleted: false,
      mistakesAnalyzed: false,
      takeawaysRecorded: false,
      status: 'completed',
      createdAt: new Date().toISOString()
    };

    const mockTestValidation = testDataValidator.validateMockTest(validMockTest, {
      mode: 'create',
      testType: 'completed',
      userId
    });

    if (!mockTestValidation.isValid) {
      errors.push(`Valid mock test failed validation: ${mockTestValidation.errors.join(', ')}`);
    }

    // Test valid upcoming test
    const validUpcomingTest: Partial<UpcomingTest> = {
      id: 'test-upcoming-1',
      name: 'Chemistry Mock Test',
      date: '2024-02-01',
      userId,
      isNotificationEnabled: true,
      syllabus: { topics: ['Organic Chemistry'], chapters: [], overallProgress: 0 },
      mistakes: [],
      takeaways: [],
      createdAt: new Date().toISOString()
    };

    const upcomingTestValidation = testDataValidator.validateUpcomingTest(validUpcomingTest, {
      mode: 'create',
      testType: 'upcoming',
      userId
    });

    if (!upcomingTestValidation.isValid) {
      errors.push(`Valid upcoming test failed validation: ${upcomingTestValidation.errors.join(', ')}`);
    }

    // Test invalid data
    const invalidMockTest: Partial<MockTest> = {
      id: '',
      name: '',
      date: 'invalid-date',
      userId: '',
      totalMarksObtained: -1,
      totalMarks: 0
    };

    const invalidValidation = testDataValidator.validateMockTest(invalidMockTest, {
      mode: 'create',
      testType: 'completed',
      userId
    });

    if (invalidValidation.isValid) {
      errors.push('Invalid mock test passed validation when it should have failed');
    }
  }

  private async validateFormDataConversion(errors: string[], warnings: string[]): Promise<void> {
    console.log('🔄 Testing form data conversion...');

    const sampleFormData: StandardTestFormData = {
      name: 'Test Form Data',
      date: new Date('2024-01-15'),
      time: '10:00',
      categoryId: 'cat-1',
      testType: 'mock',
      testEnvironment: 'home',
      duration: '180',
      totalQuestions: '100',
      testPaperUrl: 'https://example.com/test.pdf',
      targetScore: '85',
      confidenceLevel: 4,
      preparationTime: '20',
      studyMaterials: ['Textbook', 'Notes'],
      expectedDifficulty: 'medium',
      subjectInputs: [{
        subjectId: 'Physics',
        marksObtained: '85',
        totalMarks: '100',
        timeSpent: '60',
        questionsAttempted: '25'
      }],
      syllabusTopics: ['Mechanics', 'Thermodynamics'],
      syllabusChapters: [],
      mistakes: [],
      takeaways: [],
      enableMistakeTracking: true,
      enableTakeawayCollection: true,
      isNotificationEnabled: true,
      notes: 'Sample test notes'
    };

    try {
      // Test form data validation
      const formValidation = testDataValidator.validateFormData(sampleFormData, {
        mode: 'create',
        testType: 'completed',
        userId: 'test-user'
      });

      if (!formValidation.isValid) {
        errors.push(`Form data validation failed: ${formValidation.errors.join(', ')}`);
      }

      // Test conversion to unified structure
      const unifiedData = testDataSyncService.convertFormDataToUnified(sampleFormData, 'test-user');
      
      if (!unifiedData.name || !unifiedData.date || !unifiedData.userId) {
        errors.push('Form data conversion failed to preserve required fields');
      }

      // Test round-trip conversion
      const convertedBack = testDataSyncService.convertUnifiedToFormData(unifiedData);
      
      if (convertedBack.name !== sampleFormData.name || 
          convertedBack.testType !== sampleFormData.testType) {
        warnings.push('Round-trip form data conversion may have data loss');
      }

    } catch (error) {
      errors.push(`Form data conversion error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async validateTestTransitions(userId: string, errors: string[], warnings: string[]): Promise<void> {
    console.log('🔄 Testing test transition logic...');

    try {
      // Create a sample upcoming test
      const upcomingTest = unifiedUpcomingTestStorage.create(userId, {
        name: 'Transition Test',
        date: '2024-01-15',
        time: '10:00',
        userId,
        isNotificationEnabled: true,
        syllabus: { topics: ['Test Topic'], chapters: [], overallProgress: 0 },
        mistakes: [{
          id: 'mistake-1',
          category: 'conceptual',
          description: 'Sample mistake',
          severity: 'medium',
          createdAt: new Date().toISOString(),
          resolved: false
        }],
        takeaways: [{
          id: 'takeaway-1',
          category: 'strategy',
          description: 'Sample takeaway',
          priority: 'high',
          implemented: false,
          createdAt: new Date().toISOString()
        }]
      });

      // Test transition to completed
      const transitionResult = await testTransitionService.completeTest(userId, upcomingTest.id, {
        subjectMarks: [{
          subject: 'Physics',
          subjectColor: '#3b82f6',
          marksObtained: 85,
          totalMarks: 100
        }],
        totalMarksObtained: 85,
        totalMarks: 100,
        timeSpent: 120,
        notes: 'Completed via transition test'
      });

      if (!transitionResult.success) {
        errors.push(`Test transition failed: ${transitionResult.errors.join(', ')}`);
      } else if (transitionResult.completedTest) {
        // Verify data preservation
        const completedTest = transitionResult.completedTest;
        
        if (!completedTest.mistakes || completedTest.mistakes.length === 0) {
          errors.push('Test transition failed to preserve mistakes');
        }
        
        if (!completedTest.takeaways || completedTest.takeaways.length === 0) {
          errors.push('Test transition failed to preserve takeaways');
        }
        
        if (!completedTest.syllabus || completedTest.syllabus.topics.length === 0) {
          errors.push('Test transition failed to preserve syllabus');
        }

        // Clean up
        unifiedMockTestStorage.delete(userId, completedTest.id);
      }

      // Clean up
      unifiedUpcomingTestStorage.delete(userId, upcomingTest.id);

    } catch (error) {
      errors.push(`Test transition error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async validateStorageSynchronization(userId: string, errors: string[], warnings: string[]): Promise<void> {
    console.log('💾 Testing storage synchronization...');

    try {
      // Test mock test storage
      const testMockTest: MockTest = {
        id: 'sync-test-mock',
        name: 'Sync Test Mock',
        date: '2024-01-15',
        userId,
        testType: 'mock',
        targetScore: 85,
        subjectMarks: [],
        totalMarksObtained: 85,
        totalMarks: 100,
        syllabus: { topics: ['Sync Test'], chapters: [], overallProgress: 0 },
        mistakes: [],
        takeaways: [],
        difficulty: 'medium',
        isReviewed: false,
        analysisCompleted: false,
        mistakesAnalyzed: false,
        takeawaysRecorded: false,
        status: 'completed',
        createdAt: new Date().toISOString()
      };

      // Save and retrieve
      const savedMockTest = unifiedMockTestStorage.save(userId, testMockTest);
      const retrievedMockTest = unifiedMockTestStorage.getById(userId, savedMockTest.id);

      if (!retrievedMockTest) {
        errors.push('Failed to retrieve saved mock test');
      } else if (retrievedMockTest.name !== testMockTest.name) {
        errors.push('Mock test data corruption during save/retrieve');
      }

      // Test upcoming test storage
      const testUpcomingTest = unifiedUpcomingTestStorage.create(userId, {
        name: 'Sync Test Upcoming',
        date: '2024-02-01',
        userId,
        isNotificationEnabled: true,
        syllabus: { topics: ['Sync Test'], chapters: [], overallProgress: 0 },
        mistakes: [],
        takeaways: []
      });

      const retrievedUpcomingTest = unifiedUpcomingTestStorage.getById(userId, testUpcomingTest.id);

      if (!retrievedUpcomingTest) {
        errors.push('Failed to retrieve saved upcoming test');
      } else if (retrievedUpcomingTest.name !== testUpcomingTest.name) {
        errors.push('Upcoming test data corruption during save/retrieve');
      }

      // Clean up
      unifiedMockTestStorage.delete(userId, savedMockTest.id);
      unifiedUpcomingTestStorage.delete(userId, testUpcomingTest.id);

    } catch (error) {
      errors.push(`Storage synchronization error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async validateDataIntegrity(userId: string, dataIntegrityIssues: string[]): Promise<void> {
    console.log('🔍 Checking data integrity...');

    try {
      // Check for orphaned data
      const mockTests = unifiedMockTestStorage.getAll(userId);
      const upcomingTests = unifiedUpcomingTestStorage.getAll(userId);

      // Validate each mock test
      mockTests.forEach((test, index) => {
        if (!test.id || !test.name || !test.userId) {
          dataIntegrityIssues.push(`Mock test ${index + 1} missing required fields`);
        }

        if (test.totalMarksObtained > test.totalMarks) {
          dataIntegrityIssues.push(`Mock test "${test.name}" has invalid marks (obtained > total)`);
        }

        if (test.mistakes) {
          test.mistakes.forEach((mistake, mIndex) => {
            if (!mistake.id || !mistake.description) {
              dataIntegrityIssues.push(`Mock test "${test.name}" mistake ${mIndex + 1} missing required fields`);
            }
          });
        }

        if (test.takeaways) {
          test.takeaways.forEach((takeaway, tIndex) => {
            if (!takeaway.id || !takeaway.description) {
              dataIntegrityIssues.push(`Mock test "${test.name}" takeaway ${tIndex + 1} missing required fields`);
            }
          });
        }
      });

      // Validate each upcoming test
      upcomingTests.forEach((test, index) => {
        if (!test.id || !test.name || !test.userId) {
          dataIntegrityIssues.push(`Upcoming test ${index + 1} missing required fields`);
        }

        if (test.isNotificationEnabled === undefined) {
          dataIntegrityIssues.push(`Upcoming test "${test.name}" missing notification preference`);
        }
      });

    } catch (error) {
      dataIntegrityIssues.push(`Data integrity check error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async validateErrorHandling(errors: string[], warnings: string[]): Promise<void> {
    console.log('⚠️ Testing error handling...');

    try {
      // Test invalid data handling
      try {
        const invalidTest = {
          id: '',
          name: '',
          date: 'invalid',
          userId: '',
        };

        testDataValidator.validateMockTest(invalidTest, {
          mode: 'create',
          testType: 'completed',
          userId: 'test'
        });

        // Should not reach here
      } catch (error) {
        // Expected behavior
      }

      // Test storage error handling
      try {
        unifiedMockTestStorage.getById('', '');
        // Should handle gracefully
      } catch (error) {
        warnings.push('Storage error handling could be improved');
      }

    } catch (error) {
      errors.push(`Error handling validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // ============================================================================
  // PERFORMANCE TESTING
  // ============================================================================

  async runPerformanceTests(userId: string): Promise<{
    averageValidationTime: number;
    averageSaveTime: number;
    averageRetrievalTime: number;
    memoryUsage: number;
  }> {
    console.log('⚡ Running performance tests...');

    const iterations = 10;
    const validationTimes: number[] = [];
    const saveTimes: number[] = [];
    const retrievalTimes: number[] = [];

    for (let i = 0; i < iterations; i++) {
      // Validation performance
      const validationStart = Date.now();
      const testData = {
        id: `perf-test-${i}`,
        name: `Performance Test ${i}`,
        date: '2024-01-15',
        userId,
        testType: 'mock' as const,
        targetScore: 85,
        subjectMarks: [],
        totalMarksObtained: 85,
        totalMarks: 100,
        syllabus: { topics: ['Performance'], chapters: [], overallProgress: 0 },
        mistakes: [],
        takeaways: [],
        difficulty: 'medium' as const,
        isReviewed: false,
        analysisCompleted: false,
        mistakesAnalyzed: false,
        takeawaysRecorded: false,
        status: 'completed' as const,
        createdAt: new Date().toISOString()
      };

      testDataValidator.validateMockTest(testData, {
        mode: 'create',
        testType: 'completed',
        userId
      });
      validationTimes.push(Date.now() - validationStart);

      // Save performance
      const saveStart = Date.now();
      const savedTest = unifiedMockTestStorage.save(userId, testData);
      saveTimes.push(Date.now() - saveStart);

      // Retrieval performance
      const retrievalStart = Date.now();
      unifiedMockTestStorage.getById(userId, savedTest.id);
      retrievalTimes.push(Date.now() - retrievalStart);

      // Clean up
      unifiedMockTestStorage.delete(userId, savedTest.id);
    }

    return {
      averageValidationTime: validationTimes.reduce((a, b) => a + b, 0) / iterations,
      averageSaveTime: saveTimes.reduce((a, b) => a + b, 0) / iterations,
      averageRetrievalTime: retrievalTimes.reduce((a, b) => a + b, 0) / iterations,
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0
    };
  }
}

// Export singleton instance
export const testSyncValidator = TestSyncValidator.getInstance();
