import { 
  MockTest, 
  UpcomingTest, 
  TestDataValidation,
  SubjectMarks 
} from '@/types/mockTest';
import { 
  unifiedMockTestStorage, 
  unifiedUpcomingTestStorage,
  unifiedTestTransitionService 
} from './unifiedTestStorage';
import { testDataSyncService } from './testDataSyncService';
import { toast } from '@/components/ui/use-toast';

export interface TestCompletionData {
  subjectMarks: SubjectMarks[];
  totalMarksObtained: number;
  totalMarks: number;
  timeSpent?: number;
  actualDifficulty?: 'easier' | 'as_expected' | 'harder';
  timeManagement?: 'excellent' | 'good' | 'average' | 'poor';
  stressLevel?: number;
  notes?: string;
  additionalMistakes?: string[];
  additionalTakeaways?: string[];
}

export interface TestTransitionResult {
  success: boolean;
  completedTest?: MockTest;
  errors: string[];
  warnings: string[];
}

/**
 * Enhanced service for managing test transitions with data preservation
 */
export class TestTransitionService {
  private static instance: TestTransitionService;
  private transitionCallbacks: ((testId: string, result: TestTransitionResult) => void)[] = [];

  static getInstance(): TestTransitionService {
    if (!TestTransitionService.instance) {
      TestTransitionService.instance = new TestTransitionService();
    }
    return TestTransitionService.instance;
  }

  // ============================================================================
  // TEST COMPLETION WITH DATA PRESERVATION
  // ============================================================================

  async completeTest(
    userId: string,
    upcomingTestId: string,
    completionData: TestCompletionData
  ): Promise<TestTransitionResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Get the upcoming test
      const upcomingTest = unifiedUpcomingTestStorage.getById(userId, upcomingTestId);
      if (!upcomingTest) {
        return {
          success: false,
          errors: ['Upcoming test not found'],
          warnings: []
        };
      }

      // Validate completion data
      const completionValidation = this.validateCompletionData(completionData);
      if (!completionValidation.isValid) {
        return {
          success: false,
          errors: completionValidation.errors,
          warnings: completionValidation.warnings
        };
      }

      // Preserve and enhance syllabus data
      const enhancedSyllabus = this.enhanceSyllabusOnCompletion(upcomingTest);

      // Merge existing mistakes with new ones
      const allMistakes = [
        ...(upcomingTest.mistakes || []),
        ...this.convertAdditionalMistakes(completionData.additionalMistakes || [], upcomingTestId)
      ];

      // Merge existing takeaways with new ones
      const allTakeaways = [
        ...(upcomingTest.takeaways || []),
        ...this.convertAdditionalTakeaways(completionData.additionalTakeaways || [], upcomingTestId)
      ];

      // Create completed test with all preserved data
      const completedTest: MockTest = {
        // Preserve all unified data from upcoming test
        ...upcomingTest,
        
        // Add performance data
        subjectMarks: completionData.subjectMarks,
        totalMarksObtained: completionData.totalMarksObtained,
        totalMarks: completionData.totalMarks,
        timeSpent: completionData.timeSpent,
        
        // Post-test assessment
        actualDifficulty: completionData.actualDifficulty,
        timeManagement: completionData.timeManagement,
        stressLevel: completionData.stressLevel,
        
        // Enhanced data with preservation
        syllabus: enhancedSyllabus,
        mistakes: allMistakes,
        takeaways: allTakeaways,
        
        // Merge notes
        notes: this.mergeNotes(upcomingTest.notes, completionData.notes),
        
        // Ensure required fields
        testType: upcomingTest.testType || 'mock',
        targetScore: upcomingTest.targetScore || 0,
        difficulty: upcomingTest.expectedDifficulty || 'medium',
        
        // Set completion status
        status: 'completed',
        completedAt: new Date().toISOString(),
        resultEnteredAt: new Date().toISOString(),
        isFromUpcoming: true,
        upcomingTestId: upcomingTest.id,
        
        // Analysis flags
        isReviewed: false,
        analysisCompleted: allMistakes.length > 0 || allTakeaways.length > 0,
        mistakesAnalyzed: allMistakes.length > 0,
        takeawaysRecorded: allTakeaways.length > 0,
        
        // Update timestamps
        updatedAt: new Date().toISOString()
      };

      // Final validation of completed test
      const finalValidation = testDataSyncService.validateTestData(completedTest);
      if (!finalValidation.isValid) {
        return {
          success: false,
          errors: [`Completed test validation failed: ${finalValidation.errors.join(', ')}`],
          warnings: finalValidation.warnings
        };
      }

      // Save completed test
      const savedTest = unifiedMockTestStorage.save(userId, completedTest);
      
      // Remove from upcoming tests
      unifiedUpcomingTestStorage.delete(userId, upcomingTestId);
      
      // Notify callbacks
      const result: TestTransitionResult = {
        success: true,
        completedTest: savedTest,
        errors: [],
        warnings
      };
      
      this.notifyTransitionCallbacks(upcomingTestId, result);
      
      // Show success toast
      toast({
        title: "Test Completed",
        description: `${savedTest.name} has been moved to completed tests with all data preserved.`,
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const result: TestTransitionResult = {
        success: false,
        errors: [errorMessage],
        warnings
      };
      
      this.notifyTransitionCallbacks(upcomingTestId, result);
      
      // Show error toast
      toast({
        title: "Error",
        description: `Failed to complete test: ${errorMessage}`,
        variant: "destructive"
      });

      return result;
    }
  }

  // ============================================================================
  // AUTOMATIC TEST DETECTION AND TRANSITION
  // ============================================================================

  async checkAndTransitionOverdueTests(userId: string): Promise<TestTransitionResult[]> {
    const results: TestTransitionResult[] = [];
    const overdueTests = this.getOverdueTests(userId);

    for (const test of overdueTests) {
      if (test.autoTransition) {
        // Auto-transition to completed with default values
        const result = await this.completeTest(userId, test.id, {
          subjectMarks: [],
          totalMarksObtained: 0,
          totalMarks: 0,
          notes: 'Auto-completed: Test date passed'
        });
        results.push(result);
      } else {
        // Mark as missed
        const result = await this.markTestAsMissed(userId, test.id);
        results.push(result);
      }
    }

    return results;
  }

  async markTestAsMissed(userId: string, upcomingTestId: string): Promise<TestTransitionResult> {
    try {
      const missedTest = await unifiedTestTransitionService.markAsMissed(userId, upcomingTestId);
      
      const result: TestTransitionResult = {
        success: true,
        completedTest: missedTest,
        errors: [],
        warnings: ['Test was marked as missed']
      };
      
      this.notifyTransitionCallbacks(upcomingTestId, result);
      
      toast({
        title: "Test Missed",
        description: `${missedTest.name} has been marked as missed.`,
        variant: "destructive"
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const result: TestTransitionResult = {
        success: false,
        errors: [errorMessage],
        warnings: []
      };
      
      this.notifyTransitionCallbacks(upcomingTestId, result);
      return result;
    }
  }

  getOverdueTests(userId: string): UpcomingTest[] {
    const upcomingTests = unifiedUpcomingTestStorage.getAll(userId);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today

    return upcomingTests.filter(test => {
      const testDate = new Date(test.date);
      return testDate < today;
    });
  }

  // ============================================================================
  // DATA VALIDATION AND ENHANCEMENT
  // ============================================================================

  private validateCompletionData(data: TestCompletionData): TestDataValidation {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate subject marks
    if (!Array.isArray(data.subjectMarks)) {
      errors.push('Subject marks must be an array');
    } else {
      data.subjectMarks.forEach((mark, index) => {
        if (typeof mark.marksObtained !== 'number' || mark.marksObtained < 0) {
          errors.push(`Subject ${index + 1}: Invalid marks obtained`);
        }
        if (typeof mark.totalMarks !== 'number' || mark.totalMarks <= 0) {
          errors.push(`Subject ${index + 1}: Invalid total marks`);
        }
        if (mark.marksObtained > mark.totalMarks) {
          errors.push(`Subject ${index + 1}: Marks obtained cannot exceed total marks`);
        }
      });
    }

    // Validate totals
    if (typeof data.totalMarksObtained !== 'number' || data.totalMarksObtained < 0) {
      errors.push('Invalid total marks obtained');
    }
    if (typeof data.totalMarks !== 'number' || data.totalMarks <= 0) {
      errors.push('Invalid total marks');
    }
    if (data.totalMarksObtained > data.totalMarks) {
      errors.push('Total marks obtained cannot exceed total marks');
    }

    // Validate optional fields
    if (data.timeSpent !== undefined && (typeof data.timeSpent !== 'number' || data.timeSpent < 0)) {
      warnings.push('Invalid time spent value');
    }
    if (data.stressLevel !== undefined && (data.stressLevel < 1 || data.stressLevel > 5)) {
      warnings.push('Stress level should be between 1 and 5');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  private enhanceSyllabusOnCompletion(upcomingTest: UpcomingTest) {
    const syllabus = upcomingTest.syllabus || { topics: [], chapters: [], overallProgress: 0 };
    
    // Update overall progress to 100% since test is completed
    return {
      ...syllabus,
      overallProgress: 100,
      lastUpdated: new Date().toISOString()
    };
  }

  private convertAdditionalMistakes(mistakes: string[], testId: string) {
    return mistakes.map(description => ({
      id: `${testId}-mistake-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      category: 'other' as const,
      description,
      severity: 'medium' as const,
      createdAt: new Date().toISOString(),
      resolved: false
    }));
  }

  private convertAdditionalTakeaways(takeaways: string[], testId: string) {
    return takeaways.map(description => ({
      id: `${testId}-takeaway-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      category: 'other' as const,
      description,
      priority: 'medium' as const,
      implemented: false,
      createdAt: new Date().toISOString()
    }));
  }

  private mergeNotes(existingNotes?: string, newNotes?: string): string {
    const parts = [existingNotes, newNotes].filter(Boolean);
    return parts.join('\n\n');
  }

  // ============================================================================
  // CALLBACK MANAGEMENT
  // ============================================================================

  onTransition(callback: (testId: string, result: TestTransitionResult) => void): void {
    this.transitionCallbacks.push(callback);
  }

  private notifyTransitionCallbacks(testId: string, result: TestTransitionResult): void {
    this.transitionCallbacks.forEach(callback => {
      try {
        callback(testId, result);
      } catch (error) {
        console.error('Error in transition callback:', error);
      }
    });
  }
}

// Export singleton instance
export const testTransitionService = TestTransitionService.getInstance();
