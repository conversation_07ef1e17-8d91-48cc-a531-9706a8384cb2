import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import {
  X,
  Calendar,
  Clock,
  Target,
  FileText,
  ExternalLink,
  Edit2,
  Save,
  Plus,
  Trash2,
  CheckCircle2,
  AlertCircle,
  Award,
  TrendingUp,
  BarChart3,
  BookOpen,
  AlertTriangle,
  Lightbulb
} from "lucide-react";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";
import { MockTest, TestMistake, TestTakeaway, TestSyllabus } from "@/types/mockTest";
import {
  enhancedMockTestUtils,
  mistakesStorage,
  takeawaysStorage,
  syllabusStorage,
  urlUtils
} from "@/utils/mockTestLocalStorage";
import { SyllabusTracker } from "./SyllabusTracker";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

interface TestBreakdownModalProps {
  test: MockTest | null;
  isOpen: boolean;
  onClose: () => void;
  onTestUpdate?: (updatedTest: MockTest) => void;
}

export function TestBreakdownModal({ test, isOpen, onClose, onTestUpdate }: TestBreakdownModalProps) {
  const [mistakes, setMistakes] = useState<TestMistake[]>([]);
  const [takeaways, setTakeaways] = useState<TestTakeaway[]>([]);
  const [syllabus, setSyllabus] = useState<TestSyllabus | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  
  // Form states for editing
  const [editForm, setEditForm] = useState<{
    marksObtained: number;
    totalMarks: number;
    timeSpent: number;
    difficulty: 'easy' | 'medium' | 'hard' | 'very_hard';
    targetScore: number;
    isReviewed: boolean;
  }>({
    marksObtained: 0,
    totalMarks: 0,
    timeSpent: 0,
    difficulty: 'medium',
    targetScore: 0,
    isReviewed: false,
  });

  // Mistake form
  const [mistakeForm, setMistakeForm] = useState({
    subject: "",
    topic: "",
    description: "",
    solution: "",
  });

  // Takeaway form
  const [takeawayForm, setTakeawayForm] = useState<{
    category: 'strength' | 'weakness' | 'strategy' | 'concept' | 'other';
    description: string;
    priority: 'high' | 'medium' | 'low';
  }>({
    category: "strength",
    description: "",
    priority: "medium",
  });

  const [isAddingMistake, setIsAddingMistake] = useState(false);
  const [isAddingTakeaway, setIsAddingTakeaway] = useState(false);

  // Load data when test changes
  useEffect(() => {
    if (test) {
      loadTestData();
      setEditForm({
        marksObtained: test.marksObtained || 0,
        totalMarks: test.totalMarks || 0,
        timeSpent: test.timeSpent || 0,
        difficulty: test.difficulty || 'medium',
        targetScore: test.targetScore || 0,
        isReviewed: test.isReviewed || false,
      });
    }
  }, [test]);

  const loadTestData = () => {
    if (!test) return;
    
    const testMistakes = mistakesStorage.getByTest(test.id);
    const testTakeaways = takeawaysStorage.getByTest(test.id);
    const testSyllabus = syllabusStorage.get(test.id);
    
    setMistakes(testMistakes);
    setTakeaways(testTakeaways);
    setSyllabus(testSyllabus);
  };

  const handleSaveEdit = () => {
    if (!test) return;

    const updatedFields = {
      marksObtained: editForm.marksObtained,
      totalMarks: editForm.totalMarks,
      timeSpent: editForm.timeSpent,
      difficulty: editForm.difficulty,
      targetScore: editForm.targetScore,
      isReviewed: editForm.isReviewed,
      reviewedAt: editForm.isReviewed ? new Date().toISOString() : undefined,
    };

    enhancedMockTestUtils.saveEnhancedFields(test.id, updatedFields);
    
    const updatedTest = { ...test, ...updatedFields };
    onTestUpdate?.(updatedTest);
    setIsEditing(false);

    toast({
      title: "Success",
      description: "Test details updated successfully",
    });
  };

  const handleAddMistake = () => {
    if (!test || !mistakeForm.subject || !mistakeForm.description) {
      toast({
        title: "Error",
        description: "Please fill in required fields",
        variant: "destructive",
      });
      return;
    }

    const newMistake = mistakesStorage.add(test.id, {
      subject: mistakeForm.subject,
      topic: mistakeForm.topic,
      description: mistakeForm.description,
      solution: mistakeForm.solution,
    });

    setMistakes(prev => [...prev, newMistake]);
    setMistakeForm({ subject: "", topic: "", description: "", solution: "" });
    setIsAddingMistake(false);

    toast({
      title: "Success",
      description: "Mistake added successfully",
    });
  };

  const handleAddTakeaway = () => {
    if (!test || !takeawayForm.description) {
      toast({
        title: "Error",
        description: "Please enter a takeaway description",
        variant: "destructive",
      });
      return;
    }

    const newTakeaway = takeawaysStorage.add(test.id, {
      category: takeawayForm.category,
      description: takeawayForm.description,
      priority: takeawayForm.priority,
    });

    setTakeaways(prev => [...prev, newTakeaway]);
    setTakeawayForm({ category: "strength", description: "", priority: "medium" });
    setIsAddingTakeaway(false);

    toast({
      title: "Success",
      description: "Takeaway added successfully",
    });
  };

  const handleDeleteMistake = (mistakeId: string) => {
    mistakesStorage.delete(mistakeId);
    setMistakes(prev => prev.filter(m => m.id !== mistakeId));
    toast({
      title: "Success",
      description: "Mistake deleted successfully",
    });
  };

  const handleDeleteTakeaway = (takeawayId: string) => {
    takeawaysStorage.delete(takeawayId);
    setTakeaways(prev => prev.filter(t => t.id !== takeawayId));
    toast({
      title: "Success",
      description: "Takeaway deleted successfully",
    });
  };

  const toggleReviewStatus = () => {
    if (!test) return;

    const newStatus = !test.isReviewed;
    if (newStatus) {
      enhancedMockTestUtils.markAsReviewed(test.id);
    } else {
      enhancedMockTestUtils.markAsUnreviewed(test.id);
    }

    const updatedTest = { 
      ...test, 
      isReviewed: newStatus,
      reviewedAt: newStatus ? new Date().toISOString() : undefined,
    };
    onTestUpdate?.(updatedTest);

    toast({
      title: "Success",
      description: `Test marked as ${newStatus ? 'reviewed' : 'unreviewed'}`,
    });
  };

  if (!test) return null;

  const percentage = test.marksObtained && test.totalMarks 
    ? (test.marksObtained / test.totalMarks) * 100 
    : 0;

  const getPerformanceColor = (perc: number) => {
    if (perc >= 90) return "text-green-600";
    if (perc >= 75) return "text-emerald-600";
    if (perc >= 60) return "text-yellow-600";
    if (perc >= 40) return "text-orange-600";
    return "text-red-600";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        hideDefaultClose
        className="w-full max-w-4xl lg:max-w-5xl max-h-[90vh] overflow-hidden bg-background border border-border shadow-2xl"
      >
        <div className="absolute inset-0 bg-muted/10 pointer-events-none" />
        <div className="relative">
          <DialogHeader className="border-b border-border pb-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-3 mb-2">
                  <DialogTitle className="text-xl lg:text-2xl font-bold text-foreground truncate">
                    {test.name}
                  </DialogTitle>
                  {/* Inline Score Display */}
                  <div className="hidden sm:flex items-center gap-2">
                    <div className="flex items-center justify-center h-8 w-8 lg:h-10 lg:w-10 rounded-full bg-violet-600">
                      <span className="text-sm lg:text-base font-bold text-white">
                        {percentage.toFixed(0)}%
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {test.marksObtained}/{test.totalMarks}
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap items-center gap-2 lg:gap-3 text-xs lg:text-sm text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <Calendar className="h-3 w-3 lg:h-4 lg:w-4" />
                    <span className="hidden sm:inline">
                      {new Date(test.date).toLocaleDateString('en-US', {
                        weekday: 'short',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </span>
                    <span className="sm:hidden">
                      {new Date(test.date).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                  {test.timeSpent && (
                    <div className="flex items-center gap-1.5">
                      <Clock className="h-3 w-3 lg:h-4 lg:w-4" />
                      {test.timeSpent} min
                    </div>
                  )}
                  {/* Mobile Score Display */}
                  <div className="sm:hidden flex items-center gap-1.5">
                    <Target className="h-3 w-3" />
                    {percentage.toFixed(0)}% ({test.marksObtained}/{test.totalMarks})
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant={test.isReviewed ? "default" : "outline"}
                  size="sm"
                  onClick={toggleReviewStatus}
                  className={cn(
                    "gap-1.5 transition-all duration-300 text-xs lg:text-sm",
                    test.isReviewed
                      ? "bg-emerald-600 hover:bg-emerald-700 text-white"
                      : "border border-violet-300 hover:border-violet-400 hover:bg-violet-50 dark:border-violet-600 dark:hover:border-violet-500 dark:hover:bg-violet-900/20"
                  )}
                >
                  {test.isReviewed ? (
                    <>
                      <CheckCircle2 className="h-3 w-3 lg:h-4 lg:w-4" />
                      <span className="hidden sm:inline">Reviewed</span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-3 w-3 lg:h-4 lg:w-4" />
                      <span className="hidden sm:inline">Review</span>
                    </>
                  )}
                </Button>
                <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </DialogPrimitive.Close>
              </div>
            </div>
          </DialogHeader>

          <div className="overflow-y-auto max-h-[calc(90vh-180px)]">
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4 bg-muted/30 border border-border shadow-sm">
                <TabsTrigger
                  value="overview"
                  className="data-[state=active]:bg-violet-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300 text-xs lg:text-sm"
                >
                  <BarChart3 className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                  <span className="hidden sm:inline">Overview</span>
                  <span className="sm:hidden">Info</span>
                </TabsTrigger>
                <TabsTrigger
                  value="syllabus"
                  className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300 text-xs lg:text-sm"
                >
                  <BookOpen className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                  <span className="hidden sm:inline">Syllabus</span>
                  <span className="sm:hidden">Syll</span>
                </TabsTrigger>
                <TabsTrigger
                  value="mistakes"
                  className="data-[state=active]:bg-rose-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300 text-xs lg:text-sm"
                >
                  <AlertTriangle className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                  <span className="hidden sm:inline">Mistakes</span>
                  <span className="sm:hidden">Errors</span>
                </TabsTrigger>
                <TabsTrigger
                  value="takeaways"
                  className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300 text-xs lg:text-sm"
                >
                  <Lightbulb className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                  <span className="hidden sm:inline">Takeaways</span>
                  <span className="sm:hidden">Notes</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4 mt-4">
                {/* Performance Summary */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mb-4"
                >
                  <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-4 bg-muted/20 rounded-lg border border-border">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-violet-600 shadow-md">
                        <span className="text-lg font-bold text-white">
                          {percentage.toFixed(0)}%
                        </span>
                      </div>
                      <div>
                        <div className="text-lg font-bold">
                          {test.marksObtained}/{test.totalMarks}
                        </div>
                        <div className="text-xs text-muted-foreground">Overall Score</div>
                      </div>
                    </div>

                    <div className="w-full sm:w-1/2 space-y-1">
                      <div className="flex items-center justify-between text-xs">
                        <span>Progress</span>
                        {test.targetScore && (
                          <div className="flex items-center gap-1">
                            <Target className="h-3 w-3 text-muted-foreground" />
                            <span>Target: {test.targetScore}</span>
                            {percentage >= (test.targetScore / test.totalMarks) * 100 && (
                              <Award className="h-3 w-3 text-yellow-500" />
                            )}
                          </div>
                        )}
                      </div>
                      <Progress
                        value={percentage}
                        className="h-2 bg-gray-200 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                </motion.div>

                {/* Test Details Card */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                >
                  <Card className="border border-border shadow-sm bg-card">
                    <CardHeader className="py-3 px-4 border-b border-border flex flex-row items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-base">
                        <BarChart3 className="h-4 w-4 text-violet-600 dark:text-violet-400" />
                        Test Details
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsEditing(!isEditing)}
                        className={cn(
                          "h-8 px-2 transition-all duration-300",
                          isEditing
                            ? "text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
                            : "text-violet-600 hover:text-violet-700 hover:bg-violet-50 dark:text-violet-400 dark:hover:bg-violet-900/20"
                        )}
                      >
                        {isEditing ? (
                          <>
                            <X className="h-3.5 w-3.5 mr-1" />
                            <span className="text-xs">Cancel</span>
                          </>
                        ) : (
                          <>
                            <Edit2 className="h-3.5 w-3.5 mr-1" />
                            <span className="text-xs">Edit</span>
                          </>
                        )}
                      </Button>
                    </CardHeader>
                    <CardContent className="p-4">
                      {isEditing ? (
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <div>
                              <Label htmlFor="marks-obtained" className="text-xs">Marks Obtained</Label>
                              <Input
                                id="marks-obtained"
                                type="number"
                                value={editForm.marksObtained}
                                onChange={(e) => setEditForm(prev => ({
                                  ...prev,
                                  marksObtained: parseInt(e.target.value) || 0
                                }))}
                                className="h-8 text-sm"
                              />
                            </div>
                            <div>
                              <Label htmlFor="total-marks" className="text-xs">Total Marks</Label>
                              <Input
                                id="total-marks"
                                type="number"
                                value={editForm.totalMarks}
                                onChange={(e) => setEditForm(prev => ({
                                  ...prev,
                                  totalMarks: parseInt(e.target.value) || 0
                                }))}
                                className="h-8 text-sm"
                              />
                            </div>
                            <div>
                              <Label htmlFor="time-spent" className="text-xs">Time Spent (minutes)</Label>
                              <Input
                                id="time-spent"
                                type="number"
                                value={editForm.timeSpent}
                                onChange={(e) => setEditForm(prev => ({
                                  ...prev,
                                  timeSpent: parseInt(e.target.value) || 0
                                }))}
                                className="h-8 text-sm"
                              />
                            </div>
                            <div>
                              <Label htmlFor="target-score" className="text-xs">Target Score</Label>
                              <Input
                                id="target-score"
                                type="number"
                                value={editForm.targetScore}
                                onChange={(e) => setEditForm(prev => ({
                                  ...prev,
                                  targetScore: parseInt(e.target.value) || 0
                                }))}
                                className="h-8 text-sm"
                              />
                            </div>
                          </div>
                          <div>
                            <Label htmlFor="difficulty" className="text-xs">Difficulty</Label>
                            <Select
                              value={editForm.difficulty}
                              onValueChange={(value: 'easy' | 'medium' | 'hard') =>
                                setEditForm(prev => ({ ...prev, difficulty: value }))
                              }
                            >
                              <SelectTrigger className="h-8 text-sm">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="easy">Easy</SelectItem>
                                <SelectItem value="medium">Medium</SelectItem>
                                <SelectItem value="hard">Hard</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <Button onClick={handleSaveEdit} size="sm" className="gap-1 mt-2">
                            <Save className="h-3.5 w-3.5" />
                            <span className="text-xs">Save Changes</span>
                          </Button>
                        </div>
                      ) : (
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 text-sm">
                          <div className="flex flex-col gap-1">
                            <span className="text-xs text-muted-foreground">Date</span>
                            <div className="flex items-center gap-1.5">
                              <Calendar className="h-3.5 w-3.5 text-violet-600" />
                              <span>{new Date(test.date).toLocaleDateString()}</span>
                            </div>
                          </div>

                          {test.timeSpent && (
                            <div className="flex flex-col gap-1">
                              <span className="text-xs text-muted-foreground">Duration</span>
                              <div className="flex items-center gap-1.5">
                                <Clock className="h-3.5 w-3.5 text-emerald-600" />
                                <span>{test.timeSpent} min</span>
                              </div>
                            </div>
                          )}

                          {test.difficulty && (
                            <div className="flex flex-col gap-1">
                              <span className="text-xs text-muted-foreground">Difficulty</span>
                              <div className="flex items-center gap-1.5">
                                <Target className="h-3.5 w-3.5 text-rose-600" />
                                <Badge variant="outline" className="text-xs h-5 px-1.5">
                                  {test.difficulty}
                                </Badge>
                              </div>
                            </div>
                          )}

                          {test.testPaperUrl && (
                            <div className="flex flex-col gap-1">
                              <span className="text-xs text-muted-foreground">Test Paper</span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(test.testPaperUrl, '_blank')}
                                className="h-7 text-xs gap-1.5"
                              >
                                <ExternalLink className="h-3 w-3" />
                                View Paper
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Quick Stats */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                >
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                    <div className="flex items-center gap-3 p-3 bg-rose-50/50 dark:bg-rose-950/20 rounded-lg border border-rose-100 dark:border-rose-900">
                      <div className="flex items-center justify-center w-9 h-9 rounded-full bg-rose-600">
                        <AlertTriangle className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="text-lg font-bold text-rose-600 dark:text-rose-400">{mistakes.length}</div>
                        <div className="text-xs text-rose-600/70 dark:text-rose-400/70">Mistakes</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-emerald-50/50 dark:bg-emerald-950/20 rounded-lg border border-emerald-100 dark:border-emerald-900">
                      <div className="flex items-center justify-center w-9 h-9 rounded-full bg-emerald-600">
                        <Lightbulb className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="text-lg font-bold text-emerald-600 dark:text-emerald-400">{takeaways.length}</div>
                        <div className="text-xs text-emerald-600/70 dark:text-emerald-400/70">Takeaways</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-purple-50/50 dark:bg-purple-950/20 rounded-lg border border-purple-100 dark:border-purple-900">
                      <div className="flex items-center justify-center w-9 h-9 rounded-full bg-purple-600">
                        <BookOpen className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400">{syllabus?.overallProgress || 0}%</div>
                        <div className="text-xs text-purple-600/70 dark:text-purple-400/70">Syllabus</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-gray-50/50 dark:bg-gray-900/30 rounded-lg border border-gray-100 dark:border-gray-800">
                      <div className="flex items-center justify-center w-9 h-9 rounded-full bg-gray-700 dark:bg-gray-600">
                        <CheckCircle2 className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="text-lg font-bold text-gray-700 dark:text-gray-300">{test.isReviewed ? "Yes" : "No"}</div>
                        <div className="text-xs text-gray-600/70 dark:text-gray-400/70">Reviewed</div>
                      </div>
                    </div>
                  </div>
                </motion.div>
          </TabsContent>

          <TabsContent value="syllabus">
            <SyllabusTracker 
              testId={test.id} 
              testName={test.name}
              onProgressUpdate={(progress) => {
                // Update syllabus state if needed
                if (syllabus) {
                  setSyllabus({ ...syllabus, overallProgress: progress });
                }
              }}
            />
          </TabsContent>

          <TabsContent value="mistakes" className="space-y-3 mt-4">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-semibold">Mistakes & Errors</h3>
              <Button
                onClick={() => setIsAddingMistake(true)}
                size="sm"
                className="gap-1.5 h-8 text-xs"
              >
                <Plus className="h-3.5 w-3.5" />
                Add Mistake
              </Button>
            </div>

            {isAddingMistake && (
              <Card className="border border-rose-200 dark:border-rose-800">
                <CardContent className="p-3 space-y-3">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="mistake-subject" className="text-xs">Subject</Label>
                      <Input
                        id="mistake-subject"
                        value={mistakeForm.subject}
                        onChange={(e) => setMistakeForm(prev => ({ ...prev, subject: e.target.value }))}
                        placeholder="e.g., Physics, Chemistry"
                        className="h-8 text-sm"
                      />
                    </div>
                    <div>
                      <Label htmlFor="mistake-topic" className="text-xs">Topic</Label>
                      <Input
                        id="mistake-topic"
                        value={mistakeForm.topic}
                        onChange={(e) => setMistakeForm(prev => ({ ...prev, topic: e.target.value }))}
                        placeholder="e.g., Thermodynamics"
                        className="h-8 text-sm"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="mistake-description" className="text-xs">Description</Label>
                    <Textarea
                      id="mistake-description"
                      value={mistakeForm.description}
                      onChange={(e) => setMistakeForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe what went wrong..."
                      rows={2}
                      className="text-sm"
                    />
                  </div>
                  <div>
                    <Label htmlFor="mistake-solution" className="text-xs">Solution/Learning</Label>
                    <Textarea
                      id="mistake-solution"
                      value={mistakeForm.solution}
                      onChange={(e) => setMistakeForm(prev => ({ ...prev, solution: e.target.value }))}
                      placeholder="How to avoid this mistake in future..."
                      rows={2}
                      className="text-sm"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddMistake} size="sm" className="h-8 text-xs">Add Mistake</Button>
                    <Button variant="outline" onClick={() => setIsAddingMistake(false)} size="sm" className="h-8 text-xs">
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {mistakes.length === 0 ? (
              <Card>
                <CardContent className="text-center py-6">
                  <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
                  <h3 className="text-sm font-semibold mb-1">No mistakes recorded</h3>
                  <p className="text-xs text-muted-foreground">
                    Add mistakes to track areas for improvement
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2">
                {mistakes.map((mistake, index) => (
                  <motion.div
                    key={mistake.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Card className="border border-rose-100 dark:border-rose-900">
                      <CardContent className="p-3">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex flex-wrap items-center gap-1.5">
                            <Badge variant="outline" className="text-xs h-5 px-1.5">{mistake.subject}</Badge>
                            {mistake.topic && (
                              <Badge variant="secondary" className="text-xs h-5 px-1.5">{mistake.topic}</Badge>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteMistake(mistake.id)}
                            className="h-6 w-6 p-0 text-rose-600 hover:text-rose-700 hover:bg-rose-50 dark:hover:bg-rose-900/20"
                          >
                            <Trash2 className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                        <p className="text-sm mb-2">{mistake.description}</p>
                        {mistake.solution && (
                          <div className="bg-muted/50 p-2 rounded text-xs">
                            <p className="font-medium mb-1 text-emerald-600 dark:text-emerald-400">Solution:</p>
                            <p>{mistake.solution}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="takeaways" className="space-y-3 mt-4">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-semibold">Key Takeaways</h3>
              <Button
                onClick={() => setIsAddingTakeaway(true)}
                size="sm"
                className="gap-1.5 h-8 text-xs"
              >
                <Plus className="h-3.5 w-3.5" />
                Add Takeaway
              </Button>
            </div>

            {isAddingTakeaway && (
              <Card className="border border-purple-200 dark:border-purple-800">
                <CardContent className="p-3 space-y-3">
                  <div>
                    <Label htmlFor="takeaway-category" className="text-xs">Category</Label>
                    <Select
                      value={takeawayForm.category}
                      onValueChange={(value: 'strength' | 'weakness' | 'strategy' | 'concept' | 'other') =>
                        setTakeawayForm(prev => ({ ...prev, category: value }))
                      }
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="strength">Strength</SelectItem>
                        <SelectItem value="weakness">Weakness</SelectItem>
                        <SelectItem value="strategy">Strategy</SelectItem>
                        <SelectItem value="concept">Concept</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="takeaway-description" className="text-xs">Takeaway</Label>
                    <Textarea
                      id="takeaway-description"
                      value={takeawayForm.description}
                      onChange={(e) => setTakeawayForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="What did you learn from this test?"
                      rows={2}
                      className="text-sm"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddTakeaway} size="sm" className="h-8 text-xs">Add Takeaway</Button>
                    <Button variant="outline" onClick={() => setIsAddingTakeaway(false)} size="sm" className="h-8 text-xs">
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {takeaways.length === 0 ? (
              <Card>
                <CardContent className="text-center py-6">
                  <TrendingUp className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
                  <h3 className="text-sm font-semibold mb-1">No takeaways recorded</h3>
                  <p className="text-xs text-muted-foreground">
                    Add key learnings and insights from this test
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2">
                {takeaways.map((takeaway, index) => (
                  <motion.div
                    key={takeaway.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Card className="border border-purple-100 dark:border-purple-900">
                      <CardContent className="p-3">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            {takeaway.category && (
                              <Badge variant="outline" className="text-xs h-5 px-1.5 mb-2">
                                {takeaway.category}
                              </Badge>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTakeaway(takeaway.id)}
                            className="h-6 w-6 p-0 text-purple-600 hover:text-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20"
                          >
                            <Trash2 className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                        <p className="text-sm">{takeaway.description}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  </DialogContent>
</Dialog>
);
}
