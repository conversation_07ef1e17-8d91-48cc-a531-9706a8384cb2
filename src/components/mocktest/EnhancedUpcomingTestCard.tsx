import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Calendar,
  Clock,
  Edit2,
  Trash2,
  ExternalLink,
  BookOpen,
  Bell,
  BellOff,
  MoreHorizontal,
  AlertTriangle,
  Timer,
  ChevronDown
} from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Slider } from "@/components/ui/slider";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UpcomingTest, TestCategory } from "@/types/mockTest";
import {
  DDayExam,
  chapterProgressUtils,
  ChapterProgress,
  dDayStorage
} from "@/utils/mockTestLocalStorage";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface EnhancedUpcomingTestCardProps {
  test: UpcomingTest;
  dDayExam?: DDayExam;
  category?: TestCategory;
  onEdit: (test: UpcomingTest) => void;
  onDelete: (testId: string) => void;
  onToggleNotification?: (testId: string, enabled: boolean) => void;
}

export function EnhancedUpcomingTestCard({
  test,
  dDayExam,
  category,
  onEdit,
  onDelete,
  onToggleNotification
}: EnhancedUpcomingTestCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isSyllabusExpanded, setIsSyllabusExpanded] = useState(false);
  const [localChapters, setLocalChapters] = useState<ChapterProgress[]>([]);

  // Initialize chapters from D-Day exam or create from syllabus
  useEffect(() => {
    if (dDayExam?.preparationData.chapters) {
      setLocalChapters(dDayExam.preparationData.chapters);
    } else if (test.syllabus && test.syllabus.length > 0) {
      // Create chapters from syllabus topics
      const chapters: ChapterProgress[] = test.syllabus.map(topic => ({
        chapterName: topic,
        completionPercentage: 0,
        confidenceLevel: 3 as 1 | 2 | 3 | 4 | 5,
        lastStudied: undefined,
        notes: ""
      }));
      setLocalChapters(chapters);
    }
  }, [dDayExam, test.syllabus]);

  // Function to update chapter progress
  const updateChapterProgress = (chapterName: string, updates: Partial<ChapterProgress>) => {
    const updatedChapters = localChapters.map(chapter =>
      chapter.chapterName === chapterName
        ? { ...chapter, ...updates }
        : chapter
    );
    setLocalChapters(updatedChapters);

    // Save to D-Day exam if it exists
    if (dDayExam) {
      const updates = {
        preparationData: {
          ...dDayExam.preparationData,
          chapters: updatedChapters
        },
        updatedAt: new Date().toISOString()
      };
      dDayStorage.update(dDayExam.userId, dDayExam.id, updates);
    }
  };

  // Calculate days left
  const daysLeft = test.daysLeft || 0;

  // Get priority colors
  const getPriorityColors = (priority: DDayExam['priority']) => {
    switch (priority) {
      case 'critical':
        return {
          bg: 'bg-red-50 dark:bg-red-950/20',
          border: 'border-red-200 dark:border-red-800',
          text: 'text-red-700 dark:text-red-400',
          badge: 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400',
          indicator: 'bg-red-500',
          accent: 'text-red-600'
        };
      case 'high':
        return {
          bg: 'bg-orange-50 dark:bg-orange-950/20',
          border: 'border-orange-200 dark:border-orange-800',
          text: 'text-orange-700 dark:text-orange-400',
          badge: 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400',
          indicator: 'bg-orange-500',
          accent: 'text-orange-600'
        };
      case 'medium':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-950/20',
          border: 'border-yellow-200 dark:border-yellow-800',
          text: 'text-yellow-700 dark:text-yellow-400',
          badge: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400',
          indicator: 'bg-yellow-500',
          accent: 'text-yellow-600'
        };
      case 'low':
        return {
          bg: 'bg-green-50 dark:bg-green-950/20',
          border: 'border-green-200 dark:border-green-800',
          text: 'text-green-700 dark:text-green-400',
          badge: 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400',
          indicator: 'bg-green-500',
          accent: 'text-green-600'
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-950/20',
          border: 'border-gray-200 dark:border-gray-800',
          text: 'text-gray-700 dark:text-gray-400',
          badge: 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400',
          indicator: 'bg-gray-500',
          accent: 'text-gray-600'
        };
    }
  };
  
  // Get urgency styling
  const getUrgencyStyle = (days: number) => {
    if (days <= 1) return {
      border: "border-red-200 dark:border-red-800",
      bg: "from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/20",
      text: "text-red-700 dark:text-red-400",
      badge: "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
    };
    if (days <= 3) return {
      border: "border-orange-200 dark:border-orange-800",
      bg: "from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/20",
      text: "text-orange-700 dark:text-orange-400",
      badge: "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400"
    };
    if (days <= 7) return {
      border: "border-yellow-200 dark:border-yellow-800",
      bg: "from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/20",
      text: "text-yellow-700 dark:text-yellow-400",
      badge: "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"
    };
    return {
      border: "border-green-200 dark:border-green-800",
      bg: "from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/20",
      text: "text-green-700 dark:text-green-400",
      badge: "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
    };
  };

  const urgencyStyle = getUrgencyStyle(daysLeft);
  const priorityColors = dDayExam ? getPriorityColors(dDayExam.priority) : null;

  // Calculate preparation progress if D-Day exam exists
  const preparationProgress = dDayExam ?
    chapterProgressUtils.calculateOverallProgress(dDayExam.preparationData.chapters) : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, scale: 1.02 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="h-full"
    >
      <Card className={cn(
        "h-full overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 group relative",
        priorityColors ? priorityColors.bg : urgencyStyle.bg,
        priorityColors ? priorityColors.border : urgencyStyle.border
      )}>
        {/* Priority Indicator */}
        {dDayExam && priorityColors && (
          <div className={cn(
            "absolute top-0 left-0 w-1 h-full",
            priorityColors.indicator
          )} />
        )}

        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg truncate mb-1">
                {test.name}
              </h3>
              <div className="flex flex-wrap gap-1">
                {category && (
                  <Badge
                    variant="secondary"
                    className="text-xs"
                    style={{ backgroundColor: category.color + '20', color: category.color }}
                  >
                    {category.name}
                  </Badge>
                )}
                {dDayExam && priorityColors && (
                  <Badge
                    variant="secondary"
                    className={cn("text-xs font-medium", priorityColors.badge)}
                  >
                    {dDayExam.priority.toUpperCase()}
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-2 ml-2">
              {/* Notification Status */}
              <motion.div
                animate={isHovered ? { scale: 1.1 } : { scale: 1 }}
                className={cn(
                  "p-1 rounded-full",
                  test.isNotificationEnabled 
                    ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400" 
                    : "bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
                )}
              >
                {test.isNotificationEnabled ? (
                  <Bell className="h-3 w-3" />
                ) : (
                  <BellOff className="h-3 w-3" />
                )}
              </motion.div>

              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(test)}>
                    <Edit2 className="h-4 w-4 mr-2" />
                    Edit Test
                  </DropdownMenuItem>
                  
                  {onToggleNotification && (
                    <DropdownMenuItem 
                      onClick={() => onToggleNotification(test.id, !test.isNotificationEnabled)}
                    >
                      {test.isNotificationEnabled ? (
                        <>
                          <BellOff className="h-4 w-4 mr-2" />
                          Disable Notifications
                        </>
                      ) : (
                        <>
                          <Bell className="h-4 w-4 mr-2" />
                          Enable Notifications
                        </>
                      )}
                    </DropdownMenuItem>
                  )}

                  {test.testPaperUrl && (
                    <DropdownMenuItem asChild>
                      <a href={test.testPaperUrl} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Test Paper
                      </a>
                    </DropdownMenuItem>
                  )}



                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onDelete(test.id)}
                    className="text-red-600 dark:text-red-400"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Test
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">
                {format(new Date(test.date), 'MMM d, yyyy')}
              </span>
            </div>
            
            {test.time && (
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{test.time}</span>
              </div>
            )}
          </div>

          {/* Days Left Indicator */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {daysLeft <= 3 ? (
                <AlertTriangle className={cn("h-4 w-4", urgencyStyle.text)} />
              ) : (
                <Timer className={cn("h-4 w-4", urgencyStyle.text)} />
              )}
              <span className={cn("font-semibold", urgencyStyle.text)}>
                {daysLeft === 0 ? "Today!" : 
                 daysLeft === 1 ? "Tomorrow" : 
                 `${daysLeft} days left`}
              </span>
            </div>
            
            <Badge className={urgencyStyle.badge}>
              {daysLeft <= 1 ? "URGENT" : 
               daysLeft <= 3 ? "SOON" : 
               daysLeft <= 7 ? "THIS WEEK" : "UPCOMING"}
            </Badge>
          </div>

          {/* Preparation Progress (if D-Day exam exists) */}
          {dDayExam && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Preparation Progress</span>
                <span className="font-medium">{Math.round(preparationProgress)}%</span>
              </div>
              <Progress value={preparationProgress} className="h-2" />
              
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{chapterProgressUtils.getCompletionStatus(dDayExam.preparationData.chapters).completed} / {chapterProgressUtils.getCompletionStatus(dDayExam.preparationData.chapters).total} chapters</span>
                <span>Auto-calculated from sessions</span>
              </div>
            </div>
          )}

          {/* Enhanced Syllabus with Completion Tracking */}
          {localChapters.length > 0 && (
            <Collapsible open={isSyllabusExpanded} onOpenChange={setIsSyllabusExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <BookOpen className="h-4 w-4" />
                    <span>Syllabus ({localChapters.length} chapters)</span>
                    <span className="text-xs">
                      {Math.round(chapterProgressUtils.calculateOverallProgress(localChapters))}% complete
                    </span>
                  </div>
                  <ChevronDown className={cn(
                    "h-4 w-4 transition-transform",
                    isSyllabusExpanded && "rotate-180"
                  )} />
                </Button>
              </CollapsibleTrigger>

              <CollapsibleContent className="space-y-2 mt-2">
                <div className="space-y-3">
                  {localChapters.map((chapter, index) => (
                    <div key={index} className="space-y-2 p-3 rounded-lg bg-muted/30 border">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{chapter.chapterName}</span>
                        <Badge variant={chapter.completionPercentage >= 80 ? "default" : "secondary"} className="text-xs">
                          {chapter.completionPercentage}%
                        </Badge>
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>Completion</span>
                          <span>{chapter.completionPercentage}%</span>
                        </div>
                        <Slider
                          value={[chapter.completionPercentage]}
                          onValueChange={([value]) => updateChapterProgress(chapter.chapterName, { completionPercentage: value })}
                          max={100}
                          step={5}
                          className="w-full"
                        />
                      </div>

                      <div className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">Confidence:</span>
                        <div className="flex gap-1">
                          {[1, 2, 3, 4, 5].map((level) => (
                            <button
                              key={level}
                              onClick={() => updateChapterProgress(chapter.chapterName, { confidenceLevel: level as 1 | 2 | 3 | 4 | 5 })}
                              className={cn(
                                "w-4 h-4 rounded-full border transition-colors",
                                level <= chapter.confidenceLevel
                                  ? "bg-violet-500 border-violet-500"
                                  : "border-muted-foreground/30 hover:border-violet-300"
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}

          {/* Confidence Level (if D-Day exam exists) */}
          {dDayExam && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Avg Confidence</span>
              <div className="flex items-center gap-1">
                {(() => {
                  const avgConfidence = dDayExam.preparationData.chapters.length > 0
                    ? Math.round(dDayExam.preparationData.chapters.reduce((sum, ch) => sum + ch.confidenceLevel, 0) / dDayExam.preparationData.chapters.length)
                    : 3;
                  return [1, 2, 3, 4, 5].map((level) => (
                    <div
                      key={level}
                      className={cn(
                        "w-3 h-3 rounded-full",
                        level <= avgConfidence
                          ? "bg-violet-500"
                          : "bg-gray-200 dark:bg-gray-700"
                      )}
                    />
                  ));
                })()}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(test)}
              className="flex-1"
            >
              <Edit2 className="h-4 w-4 mr-2" />
              Edit
            </Button>
            

          </div>
        </CardContent>

        {/* Hover Effect Overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 0.05 : 0 }}
          className="absolute inset-0 bg-violet-500/10 pointer-events-none"
        />
      </Card>
    </motion.div>
  );
}
