import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarIcon, Plus, X, BookOpen, Target, Clock, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { 
  StandardTestFormData, 
  TestType, 
  TestEnvironment, 
  ConfidenceLevel, 
  DifficultyLevel,
  UnifiedMistake,
  UnifiedTakeaway,
  SyllabusChapter,
  TestCategory
} from '@/types/mockTest';
import { testDataSyncService } from '@/utils/testDataSyncService';

export interface StandardTestFormProps {
  mode: 'create' | 'edit';
  testType: 'upcoming' | 'completed';
  initialData?: Partial<StandardTestFormData>;
  categories: TestCategory[];
  onSubmit: (data: StandardTestFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export function StandardTestForm({
  mode,
  testType,
  initialData,
  categories,
  onSubmit,
  onCancel,
  isSubmitting = false
}: StandardTestFormProps) {
  const [formData, setFormData] = useState<StandardTestFormData>({
    name: '',
    date: new Date(),
    time: '09:00',
    categoryId: '',
    testType: 'mock',
    testEnvironment: 'home',
    duration: '',
    totalQuestions: '',
    testPaperUrl: '',
    targetScore: '',
    confidenceLevel: 3,
    preparationTime: '',
    studyMaterials: [],
    expectedDifficulty: 'medium',
    subjectInputs: [{ subjectId: '', marksObtained: '', totalMarks: '', timeSpent: '', questionsAttempted: '' }],
    syllabusTopics: [],
    syllabusChapters: [],
    mistakes: [],
    takeaways: [],
    enableMistakeTracking: true,
    enableTakeawayCollection: true,
    isNotificationEnabled: true,
    notes: '',
    ...initialData
  });

  const [currentTab, setCurrentTab] = useState('basic');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Update form data when initial data changes
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData]);

  // ============================================================================
  // FORM VALIDATION
  // ============================================================================

  const validateForm = (): boolean => {
    const errors: string[] = [];

    // Basic validation
    if (!formData.name.trim()) errors.push('Test name is required');
    if (!formData.date) errors.push('Test date is required');

    // Subject validation for completed tests
    if (testType === 'completed') {
      const hasValidSubject = formData.subjectInputs.some(input =>
        input.subjectId && input.marksObtained && input.totalMarks
      );
      if (!hasValidSubject) errors.push('At least one subject with marks is required');

      // Validate marks
      formData.subjectInputs.forEach((input, index) => {
        if (input.subjectId && input.marksObtained && input.totalMarks) {
          const obtained = parseFloat(input.marksObtained);
          const total = parseFloat(input.totalMarks);
          if (obtained > total) {
            errors.push(`Subject ${index + 1}: Obtained marks cannot exceed total marks`);
          }
        }
      });
    }

    // Target score validation
    if (formData.targetScore && parseInt(formData.targetScore) < 0) {
      errors.push('Target score must be positive');
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  // ============================================================================
  // FORM HANDLERS
  // ============================================================================

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const updateFormData = (updates: Partial<StandardTestFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // ============================================================================
  // DYNAMIC FIELD HANDLERS
  // ============================================================================

  const addSubjectInput = () => {
    updateFormData({
      subjectInputs: [...formData.subjectInputs, { 
        subjectId: '', marksObtained: '', totalMarks: '', timeSpent: '', questionsAttempted: '' 
      }]
    });
  };

  const removeSubjectInput = (index: number) => {
    updateFormData({
      subjectInputs: formData.subjectInputs.filter((_, i) => i !== index)
    });
  };

  const updateSubjectInput = (index: number, field: string, value: string) => {
    const updated = formData.subjectInputs.map((input, i) =>
      i === index ? { ...input, [field]: value } : input
    );
    updateFormData({ subjectInputs: updated });
  };

  const addSyllabusTopics = (topics: string[]) => {
    updateFormData({
      syllabusTopics: [...new Set([...formData.syllabusTopics, ...topics])]
    });
  };

  const removeSyllabusTopic = (topic: string) => {
    updateFormData({
      syllabusTopics: formData.syllabusTopics.filter(t => t !== topic)
    });
  };

  const addStudyMaterial = (material: string) => {
    if (material.trim() && !formData.studyMaterials.includes(material.trim())) {
      updateFormData({
        studyMaterials: [...formData.studyMaterials, material.trim()]
      });
    }
  };

  const removeStudyMaterial = (material: string) => {
    updateFormData({
      studyMaterials: formData.studyMaterials.filter(m => m !== material)
    });
  };

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const renderValidationErrors = () => {
    if (validationErrors.length === 0) return null;

    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-4">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-red-800">Please fix the following errors:</p>
              <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderBasicInfo = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Test Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => updateFormData({ name: e.target.value })}
            placeholder="Enter test name"
          />
        </div>

        <div className="space-y-2">
          <Label>Test Date *</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.date && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.date ? format(formData.date, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.date}
                onSelect={(date) => updateFormData({ date: date || new Date() })}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="time">Time</Label>
          <Input
            id="time"
            type="time"
            value={formData.time}
            onChange={(e) => updateFormData({ time: e.target.value })}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select value={formData.categoryId} onValueChange={(value) => updateFormData({ categoryId: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="testType">Test Type</Label>
          <Select value={formData.testType} onValueChange={(value: TestType) => updateFormData({ testType: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="practice">Practice Test</SelectItem>
              <SelectItem value="mock">Mock Test</SelectItem>
              <SelectItem value="actual_exam">Actual Exam</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="testEnvironment">Environment</Label>
          <Select value={formData.testEnvironment} onValueChange={(value: TestEnvironment) => updateFormData({ testEnvironment: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="home">Home</SelectItem>
              <SelectItem value="center">Test Center</SelectItem>
              <SelectItem value="online">Online</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="duration">Duration (minutes)</Label>
          <Input
            id="duration"
            type="number"
            value={formData.duration}
            onChange={(e) => updateFormData({ duration: e.target.value })}
            placeholder="180"
          />
        </div>
      </div>
    </div>
  );

  const renderSubjectsSection = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Subject Performance</h3>
        <Button type="button" onClick={addSubjectInput} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Subject
        </Button>
      </div>

      {formData.subjectInputs.map((input, index) => (
        <Card key={index}>
          <CardContent className="pt-4">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="space-y-2">
                <Label>Subject</Label>
                <Input
                  value={input.subjectId}
                  onChange={(e) => updateSubjectInput(index, 'subjectId', e.target.value)}
                  placeholder="Subject name"
                />
              </div>

              <div className="space-y-2">
                <Label>Marks Obtained</Label>
                <Input
                  type="number"
                  value={input.marksObtained}
                  onChange={(e) => updateSubjectInput(index, 'marksObtained', e.target.value)}
                  placeholder="85"
                />
              </div>

              <div className="space-y-2">
                <Label>Total Marks</Label>
                <Input
                  type="number"
                  value={input.totalMarks}
                  onChange={(e) => updateSubjectInput(index, 'totalMarks', e.target.value)}
                  placeholder="100"
                />
              </div>

              <div className="space-y-2">
                <Label>Time Spent (min)</Label>
                <Input
                  type="number"
                  value={input.timeSpent}
                  onChange={(e) => updateSubjectInput(index, 'timeSpent', e.target.value)}
                  placeholder="60"
                />
              </div>

              <div className="space-y-2 flex items-end">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeSubjectInput(index)}
                  disabled={formData.subjectInputs.length === 1}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="targetScore">Target Score</Label>
          <Input
            id="targetScore"
            type="number"
            value={formData.targetScore}
            onChange={(e) => updateFormData({ targetScore: e.target.value })}
            placeholder="85"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="confidenceLevel">Confidence Level (1-5)</Label>
          <Select
            value={formData.confidenceLevel.toString()}
            onValueChange={(value) => updateFormData({ confidenceLevel: parseInt(value) as ConfidenceLevel })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1 - Very Low</SelectItem>
              <SelectItem value="2">2 - Low</SelectItem>
              <SelectItem value="3">3 - Medium</SelectItem>
              <SelectItem value="4">4 - High</SelectItem>
              <SelectItem value="5">5 - Very High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="expectedDifficulty">Expected Difficulty</Label>
          <Select
            value={formData.expectedDifficulty}
            onValueChange={(value: DifficultyLevel) => updateFormData({ expectedDifficulty: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="easy">Easy</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="hard">Hard</SelectItem>
              <SelectItem value="very_hard">Very Hard</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );

  const renderSyllabusSection = () => {
    const [newTopic, setNewTopic] = useState('');

    const handleAddTopic = () => {
      if (newTopic.trim()) {
        addSyllabusTopics([newTopic.trim()]);
        setNewTopic('');
      }
    };

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          <h3 className="text-lg font-medium">Syllabus Coverage</h3>
        </div>

        <div className="flex gap-2">
          <Input
            value={newTopic}
            onChange={(e) => setNewTopic(e.target.value)}
            placeholder="Add topic or chapter"
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTopic())}
          />
          <Button type="button" onClick={handleAddTopic}>
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex flex-wrap gap-2">
          {formData.syllabusTopics.map((topic, index) => (
            <Badge key={index} variant="secondary" className="flex items-center gap-1">
              {topic}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => removeSyllabusTopic(topic)}
              />
            </Badge>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="preparationTime">Preparation Time (hours)</Label>
            <Input
              id="preparationTime"
              type="number"
              step="0.5"
              value={formData.preparationTime}
              onChange={(e) => updateFormData({ preparationTime: e.target.value })}
              placeholder="20"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="testPaperUrl">Test Paper URL</Label>
            <Input
              id="testPaperUrl"
              type="url"
              value={formData.testPaperUrl}
              onChange={(e) => updateFormData({ testPaperUrl: e.target.value })}
              placeholder="https://..."
            />
          </div>
        </div>
      </div>
    );
  };

  const renderAnalysisSection = () => {
    const [newMaterial, setNewMaterial] = useState('');

    const handleAddMaterial = () => {
      if (newMaterial.trim()) {
        addStudyMaterial(newMaterial.trim());
        setNewMaterial('');
      }
    };

    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          <h3 className="text-lg font-medium">Analysis & Tracking</h3>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Study Materials</Label>
            <div className="flex gap-2">
              <Input
                value={newMaterial}
                onChange={(e) => setNewMaterial(e.target.value)}
                placeholder="Add study material"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddMaterial())}
              />
              <Button type="button" onClick={handleAddMaterial}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.studyMaterials.map((material, index) => (
                <Badge key={index} variant="outline" className="flex items-center gap-1">
                  {material}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeStudyMaterial(material)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="enableMistakeTracking"
                checked={formData.enableMistakeTracking}
                onCheckedChange={(checked) => updateFormData({ enableMistakeTracking: !!checked })}
              />
              <Label htmlFor="enableMistakeTracking">Enable mistake tracking</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="enableTakeawayCollection"
                checked={formData.enableTakeawayCollection}
                onCheckedChange={(checked) => updateFormData({ enableTakeawayCollection: !!checked })}
              />
              <Label htmlFor="enableTakeawayCollection">Enable takeaway collection</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isNotificationEnabled"
                checked={formData.isNotificationEnabled}
                onCheckedChange={(checked) => updateFormData({ isNotificationEnabled: !!checked })}
              />
              <Label htmlFor="isNotificationEnabled">Enable notifications</Label>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => updateFormData({ notes: e.target.value })}
              placeholder="Add any additional notes about this test..."
              rows={4}
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {renderValidationErrors()}
      
      <Tabs value={currentTab} onValueChange={setCurrentTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="subjects">Subjects</TabsTrigger>
          <TabsTrigger value="syllabus">Syllabus</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          {renderBasicInfo()}
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          {renderSubjectsSection()}
        </TabsContent>

        <TabsContent value="syllabus" className="space-y-4">
          {renderSyllabusSection()}
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          {renderAnalysisSection()}
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : mode === 'create' ? 'Create Test' : 'Update Test'}
        </Button>
      </div>
    </form>
  );
}
