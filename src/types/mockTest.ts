export interface SubjectMarks {
  subject: string;
  subjectColor?: string;
  marksObtained: number;
  totalMarks: number;
}

// Enhanced interfaces for new features
export interface TestCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
  userId: string;
  createdAt: string;
}

export interface ChapterProgress {
  chapterId: string;
  chapterName: string;
  subject: string;
  isCompleted: boolean;
  completedAt?: string;
  notes?: string;
}

export interface TestSyllabus {
  testId: string;
  chapters: ChapterProgress[];
  overallProgress: number; // Percentage of completed chapters
  lastUpdated: string;
}

export interface TestMistake {
  id: string;
  subject: string;
  topic: string;
  description: string;
  solution?: string;
  tags?: string[];
  createdAt: string;
}

export interface TestTakeaway {
  id: string;
  category: 'strength' | 'weakness' | 'strategy' | 'concept' | 'other';
  subject?: string;
  description: string;
  actionPlan?: string;
  priority: 'high' | 'medium' | 'low';
  createdAt: string;
}

// ============================================================================
// UNIFIED DATA MODEL FOR MOCK TEST SYNCHRONIZATION
// ============================================================================

// Unified Test Data Structure - shared between UpcomingTest and MockTest
export interface UnifiedTestData {
  // Basic Information
  id: string;
  name: string;
  date: string; // YYYY-MM-DD format
  time?: string; // HH:MM format
  userId: string;
  createdAt: string;
  updatedAt?: string;

  // Test Configuration
  testType?: TestType;
  testEnvironment?: TestEnvironment;
  duration?: number; // Test duration in minutes
  totalQuestions?: number; // Total number of questions
  categoryId?: string;
  testPaperUrl?: string;

  // Goals and Preparation
  targetScore?: number;
  confidenceLevel?: ConfidenceLevel;
  preparationTime?: number; // Hours spent preparing
  studyMaterials?: string[];
  expectedDifficulty?: DifficultyLevel;

  // Unified Syllabus Structure
  syllabus: UnifiedSyllabus;

  // Analysis Data (unified structure)
  mistakes: UnifiedMistake[];
  takeaways: UnifiedTakeaway[];

  // Notes and Additional Info
  notes?: string;

  // Workflow Settings
  isNotificationEnabled?: boolean;
  autoTransition?: boolean;
  preparationReminders?: boolean;
  reminderIntervals?: number[];
}

// Unified Syllabus Structure
export interface UnifiedSyllabus {
  topics: string[]; // List of topics/chapters
  chapters?: SyllabusChapter[]; // Detailed chapter breakdown
  overallProgress?: number; // 0-100 percentage
  lastUpdated?: string;
}

export interface SyllabusChapter {
  id: string;
  name: string;
  topics: string[];
  completed: boolean;
  confidence: ConfidenceLevel;
  timeSpent?: number; // Minutes spent on this chapter
  lastStudied?: string;
  notes?: string;
}

// Unified Mistake Structure
export interface UnifiedMistake {
  id: string;
  category: 'conceptual' | 'calculation' | 'silly' | 'time_management' | 'other';
  subject?: string;
  topic?: string;
  description: string;
  solution?: string;
  preventionStrategy?: string;
  severity: 'low' | 'medium' | 'high';
  createdAt: string;
  resolved?: boolean;
  resolvedAt?: string;
}

// Unified Takeaway Structure
export interface UnifiedTakeaway {
  id: string;
  category: 'strength' | 'weakness' | 'strategy' | 'concept' | 'time_management' | 'other';
  subject?: string;
  topic?: string;
  description: string;
  actionPlan?: string;
  priority: 'high' | 'medium' | 'low';
  implemented?: boolean;
  implementedAt?: string;
  createdAt: string;
}

// ============================================================================
// EXISTING INTERFACES (Updated to use unified structure)
// ============================================================================

// Enhanced UpcomingTest interface using unified structure
export interface UpcomingTest extends Omit<UnifiedTestData, 'syllabus' | 'mistakes' | 'takeaways'> {
  // Override with optional unified structures for upcoming tests
  syllabus?: UnifiedSyllabus;
  mistakes?: UnifiedMistake[];
  takeaways?: UnifiedTakeaway[];

  // Upcoming test specific fields
  daysLeft?: number;
  isNotificationEnabled: boolean; // Required for upcoming tests

  // Status tracking for upcoming tests
  status?: 'scheduled' | 'preparation' | 'ready' | 'in_progress';
  lastStudySession?: string;

  // Analysis setup
  enableMistakeTracking?: boolean;
  enableTakeawayCollection?: boolean;
  reviewReminderEnabled?: boolean;

  // Backward compatibility - will be migrated to unified structure
  syllabus_legacy?: string[]; // For migration purposes
}

// Test status for lifecycle management
export type TestStatus = 'upcoming' | 'in_progress' | 'completed_pending' | 'completed' | 'missed';

// Test type classification
export type TestType = 'practice' | 'mock' | 'actual_exam';

// Test environment
export type TestEnvironment = 'home' | 'center' | 'online';

// Confidence and difficulty levels
export type ConfidenceLevel = 1 | 2 | 3 | 4 | 5;
export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'very_hard';
export type DifficultyAssessment = 'easier' | 'as_expected' | 'harder';
export type TimeManagement = 'excellent' | 'good' | 'average' | 'poor';

// Subject-wise performance details
export interface SubjectPerformance {
  subject: string;
  subjectColor?: string;
  marksObtained: number;
  totalMarks: number;
  timeSpent?: number; // Time spent on this subject in minutes
  questionsAttempted?: number;
  questionsCorrect?: number;
  accuracy?: number; // Percentage accuracy
}

// Enhanced MockTest interface using unified structure
export interface MockTest extends UnifiedTestData {
  // Required fields for completed tests
  testType: TestType; // Required for mock tests
  targetScore: number; // Required for mock tests
  syllabus: UnifiedSyllabus; // Required - use unified structure
  mistakes: UnifiedMistake[]; // Required - use unified structure
  takeaways: UnifiedTakeaway[]; // Required - use unified structure

  // Performance Data (specific to completed tests)
  subjectMarks: SubjectMarks[]; // Array of subjects with their marks (backward compatibility)
  totalMarksObtained: number; // Total marks obtained across all subjects
  totalMarks: number; // Total marks across all subjects
  subjectPerformance?: SubjectPerformance[]; // Detailed subject-wise performance
  timeSpent?: number; // Actual time spent on test in minutes
  timeSpentPerSubject?: { [subject: string]: number }; // Time per subject

  // Computed properties for backward compatibility
  marksObtained?: number; // Alias for totalMarksObtained

  // Post-test Assessment
  difficulty: DifficultyLevel; // Perceived difficulty level
  actualDifficulty?: DifficultyAssessment; // Compared to expectation
  timeManagement?: TimeManagement; // How well time was managed
  stressLevel?: ConfidenceLevel; // Stress level during test (1-5)

  // Analysis and Review
  isReviewed: boolean; // Whether the test has been reviewed/analyzed
  reviewedAt?: string; // When the test was reviewed
  analysisCompleted: boolean; // Whether full analysis is done
  mistakesAnalyzed: boolean; // Whether mistakes have been analyzed
  takeawaysRecorded: boolean; // Whether takeaways have been recorded
  reviewScheduled?: string; // When review is scheduled

  // Status and Workflow
  status: TestStatus; // Current status of the test
  completedAt?: string; // When the test was completed
  resultEnteredAt?: string; // When results were entered
  isFromUpcoming?: boolean; // Whether this test was created from upcoming tests
  upcomingTestId?: string; // Reference to original upcoming test

  // Legacy fields for backward compatibility (will be migrated)
  syllabus_legacy?: string[]; // For migration purposes
  mistakes_legacy?: TestMistake[]; // For migration purposes
  takeaways_legacy?: TestTakeaway[]; // For migration purposes
}

// ============================================================================
// DATA SYNCHRONIZATION AND VALIDATION INTERFACES
// ============================================================================

// Validation result for test data
export interface TestDataValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Synchronization status for test data
export interface SyncStatus {
  lastSynced?: string;
  syncInProgress: boolean;
  syncErrors: string[];
  localChanges: boolean;
  remoteChanges: boolean;
}

// Test data synchronization interface
export interface TestSyncData {
  test: MockTest | UpcomingTest;
  syncStatus: SyncStatus;
  validation: TestDataValidation;
}

// Migration interface for converting legacy data to unified structure
export interface TestDataMigration {
  fromLegacy(legacyTest: any): UnifiedTestData;
  toLegacy(unifiedTest: UnifiedTestData): any;
  validateMigration(original: any, migrated: UnifiedTestData): TestDataValidation;
}

// Form data interface for standardized input across all components
export interface StandardTestFormData {
  // Basic Information
  name: string;
  date: Date;
  time: string;
  categoryId?: string;
  testType: TestType;

  // Test Configuration
  testEnvironment?: TestEnvironment;
  duration?: string;
  totalQuestions?: string;
  testPaperUrl?: string;

  // Goals and Preparation
  targetScore?: string;
  confidenceLevel?: ConfidenceLevel;
  preparationTime?: string;
  studyMaterials: string[];
  expectedDifficulty?: DifficultyLevel;

  // Subject Details
  subjectInputs: SubjectMarkInput[];

  // Unified Syllabus
  syllabusTopics: string[];
  syllabusChapters: SyllabusChapter[];

  // Analysis Data
  mistakes: UnifiedMistake[];
  takeaways: UnifiedTakeaway[];

  // Settings
  enableMistakeTracking: boolean;
  enableTakeawayCollection: boolean;
  isNotificationEnabled: boolean;

  // Notes
  notes: string;
}

export interface MockTestAnalytics {
  totalTests: number;
  averageScore: number;
  highestScore: {
    testId: string;
    testName: string;
    score: number;
    percentage: number;
  };
  lowestScore: {
    testId: string;
    testName: string;
    score: number;
    percentage: number;
  };
  subjectPerformance: {
    [subject: string]: {
      totalTests: number;
      averageScore: number;
      averagePercentage: number;
    }
  };
  recentTests: MockTest[];

  // Enhanced analytics
  categoryPerformance: {
    [categoryId: string]: {
      categoryName: string;
      totalTests: number;
      averageScore: number;
      averagePercentage: number;
      trend: 'improving' | 'declining' | 'stable';
    }
  };
  performanceTrend: {
    date: string;
    score: number;
    percentage: number;
    testName: string;
    categoryId?: string;
  }[];
  reviewedTestsCount: number;
  unreviewedTestsCount: number;
  mistakesBySubject: {
    [subject: string]: number;
  };
  strengthsBySubject: {
    [subject: string]: number;
  };
}