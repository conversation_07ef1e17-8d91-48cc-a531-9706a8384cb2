export interface SubjectMarks {
  subject: string;
  subjectColor?: string;
  marksObtained: number;
  totalMarks: number;
}

// Enhanced interfaces for new features
export interface TestCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
  userId: string;
  createdAt: string;
}

export interface ChapterProgress {
  chapterId: string;
  chapterName: string;
  subject: string;
  isCompleted: boolean;
  completedAt?: string;
  notes?: string;
}

export interface TestSyllabus {
  testId: string;
  chapters: ChapterProgress[];
  overallProgress: number; // Percentage of completed chapters
  lastUpdated: string;
}

export interface TestMistake {
  id: string;
  subject: string;
  topic: string;
  description: string;
  solution?: string;
  tags?: string[];
  createdAt: string;
}

export interface TestTakeaway {
  id: string;
  category: 'strength' | 'weakness' | 'strategy' | 'concept' | 'other';
  subject?: string;
  description: string;
  actionPlan?: string;
  priority: 'high' | 'medium' | 'low';
  createdAt: string;
}

// Enhanced UpcomingTest interface with comprehensive features
export interface UpcomingTest {
  id: string;
  name: string;
  date: string; // YYYY-MM-DD format
  time?: string; // HH:MM format
  categoryId?: string;
  syllabus?: string[];
  testPaperUrl?: string;
  isNotificationEnabled: boolean;
  daysLeft?: number;
  userId: string;
  createdAt: string;

  // Enhanced Test Configuration
  testType?: TestType; // Type of test
  testEnvironment?: TestEnvironment; // Where the test will be taken
  duration?: number; // Expected test duration in minutes
  totalQuestions?: number; // Expected number of questions

  // Goals and Preparation
  targetScore?: number; // Target score for this test
  confidenceLevel?: ConfidenceLevel; // Current confidence level (1-5)
  preparationTime?: number; // Hours spent preparing so far
  studyMaterials?: string[]; // Materials being used for preparation
  expectedDifficulty?: DifficultyLevel; // Expected difficulty level

  // Workflow and Automation
  autoTransition?: boolean; // Auto-move to completed when date passes
  preparationReminders?: boolean; // Enable preparation reminders
  reminderIntervals?: number[]; // Days before test to send reminders

  // Status tracking
  status?: 'scheduled' | 'preparation' | 'ready' | 'in_progress'; // Preparation status
  lastStudySession?: string; // Last time user studied for this test

  // Analysis setup
  enableMistakeTracking?: boolean; // Enable mistake tracking for this test
  enableTakeawayCollection?: boolean; // Enable takeaway collection
  reviewReminderEnabled?: boolean; // Enable post-test review reminders
}

// Test status for lifecycle management
export type TestStatus = 'upcoming' | 'in_progress' | 'completed_pending' | 'completed' | 'missed';

// Test type classification
export type TestType = 'practice' | 'mock' | 'actual_exam';

// Test environment
export type TestEnvironment = 'home' | 'center' | 'online';

// Confidence and difficulty levels
export type ConfidenceLevel = 1 | 2 | 3 | 4 | 5;
export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'very_hard';
export type DifficultyAssessment = 'easier' | 'as_expected' | 'harder';
export type TimeManagement = 'excellent' | 'good' | 'average' | 'poor';

// Subject-wise performance details
export interface SubjectPerformance {
  subject: string;
  subjectColor?: string;
  marksObtained: number;
  totalMarks: number;
  timeSpent?: number; // Time spent on this subject in minutes
  questionsAttempted?: number;
  questionsCorrect?: number;
  accuracy?: number; // Percentage accuracy
}

// Enhanced MockTest interface with comprehensive features
export interface MockTest {
  id: string;          // Unique ID for the mock test
  name: string;        // Name of the mock test
  date: string;        // Date of the test in YYYY-MM-DD format
  time?: string;       // Time of the test in HH:MM format
  subjectMarks: SubjectMarks[]; // Array of subjects with their marks (backward compatibility)
  totalMarksObtained: number; // Total marks obtained across all subjects
  totalMarks: number;  // Total marks across all subjects
  notes?: string;      // Optional notes about the test
  createdAt: string;   // Timestamp when the test was created
  userId: string;      // User ID who created the test

  // Computed properties for backward compatibility
  marksObtained?: number; // Alias for totalMarksObtained

  // Enhanced Test Configuration
  testType: TestType;  // Type of test
  testEnvironment?: TestEnvironment; // Where the test was taken
  duration?: number;   // Test duration in minutes
  totalQuestions?: number; // Total number of questions
  categoryId?: string; // Reference to TestCategory
  testPaperUrl?: string; // URL to test paper PDF

  // Performance and Analysis
  subjectPerformance?: SubjectPerformance[]; // Detailed subject-wise performance
  timeSpent?: number; // Actual time spent on test in minutes
  timeSpentPerSubject?: { [subject: string]: number }; // Time per subject

  // Pre-test Data
  targetScore: number; // Target score for this test
  confidenceLevel?: ConfidenceLevel; // Confidence before test (1-5)
  preparationTime?: number; // Hours spent preparing
  studyMaterials?: string[]; // Materials used for preparation
  syllabus?: string[]; // Topics or chapters covered in the test

  // Post-test Assessment
  difficulty: DifficultyLevel; // Perceived difficulty level
  actualDifficulty?: DifficultyAssessment; // Compared to expectation
  timeManagement?: TimeManagement; // How well time was managed
  stressLevel?: ConfidenceLevel; // Stress level during test (1-5)

  // Analysis and Review
  isReviewed: boolean; // Whether the test has been reviewed/analyzed
  reviewedAt?: string; // When the test was reviewed
  mistakes: TestMistake[]; // Array of mistakes made in this test
  takeaways: TestTakeaway[]; // Array of key takeaways from this test
  analysisCompleted: boolean; // Whether full analysis is done
  mistakesAnalyzed: boolean; // Whether mistakes have been analyzed
  takeawaysRecorded: boolean; // Whether takeaways have been recorded
  reviewScheduled?: string; // When review is scheduled

  // Status and Workflow
  status: TestStatus; // Current status of the test
  completedAt?: string; // When the test was completed
  resultEnteredAt?: string; // When results were entered
  isFromUpcoming?: boolean; // Whether this test was created from upcoming tests
  upcomingTestId?: string; // Reference to original upcoming test

  // Legacy fields for backward compatibility
  updatedAt?: string; // Last updated timestamp
}

export interface MockTestAnalytics {
  totalTests: number;
  averageScore: number;
  highestScore: {
    testId: string;
    testName: string;
    score: number;
    percentage: number;
  };
  lowestScore: {
    testId: string;
    testName: string;
    score: number;
    percentage: number;
  };
  subjectPerformance: {
    [subject: string]: {
      totalTests: number;
      averageScore: number;
      averagePercentage: number;
    }
  };
  recentTests: MockTest[];

  // Enhanced analytics
  categoryPerformance: {
    [categoryId: string]: {
      categoryName: string;
      totalTests: number;
      averageScore: number;
      averagePercentage: number;
      trend: 'improving' | 'declining' | 'stable';
    }
  };
  performanceTrend: {
    date: string;
    score: number;
    percentage: number;
    testName: string;
    categoryId?: string;
  }[];
  reviewedTestsCount: number;
  unreviewedTestsCount: number;
  mistakesBySubject: {
    [subject: string]: number;
  };
  strengthsBySubject: {
    [subject: string]: number;
  };
}